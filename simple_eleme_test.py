#!/usr/bin/env python3
"""
Simple test for Eleme API endpoints
"""

import time
import json
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By


def test_api_endpoints():
    """Test the discovered API endpoints"""
    print("🚀 Testing Eleme API Endpoints...")
    
    session = requests.Session()
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://h5.ele.me/',
        'Origin': 'https://h5.ele.me'
    }
    
    print("\n" + "="*50)
    print("STEP 1: TESTING MAIN PAGE ACCESS")
    print("="*50)
    
    try:
        response = session.get('https://h5.ele.me/', headers=headers, timeout=10)
        print(f"Main page status: {response.status_code}")
        print(f"Response length: {len(response.text)}")
        print(f"Final URL: {response.url}")
        
        if "登录" in response.text:
            print("⚠️  Page requires login")
        else:
            print("✅ Page accessible without login")
            
    except Exception as e:
        print(f"❌ Error accessing main page: {e}")
    
    print("\n" + "="*50)
    print("STEP 2: TESTING API ENDPOINTS")
    print("="*50)
    
    # Test discovered API endpoints
    api_tests = [
        {
            'name': 'Store Detail Query',
            'url': 'https://alsc-buy2.ele.me/h5/mtop.alsc.waimai.store.detail.mstie.scheme.query/1.0/',
            'params': {
                'jsv': '2.6.2',
                'appKey': '12574478',
                't': str(int(time.time() * 1000)),
                'api': 'mtop.alsc.waimai.store.detail.mstie.scheme.query',
                'v': '1.0',
                'dataType': 'json',
                'type': 'originaljson',
                'data': json.dumps({
                    'type': 1,
                    'defaultSwitch': True
                })
            }
        },
        {
            'name': 'User Session Check',
            'url': 'https://waimai-guide.ele.me/h5/mtop.alsc.user.session.ele.check/1.0/',
            'params': {
                'jsv': '2.7.5',
                'appKey': '12574478',
                't': str(int(time.time() * 1000)),
                'api': 'mtop.alsc.user.session.ele.check',
                'v': '1.0',
                'type': 'originaljson',
                'dataType': 'json',
                'data': '{}'
            }
        }
    ]
    
    for test in api_tests:
        print(f"\n--- Testing {test['name']} ---")
        try:
            response = session.get(test['url'], params=test['params'], headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"Response type: {type(data)}")
                    if isinstance(data, dict):
                        print(f"Keys: {list(data.keys())}")
                        if 'data' in data:
                            print(f"Data content: {str(data['data'])[:200]}...")
                    print(f"Full response: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                except json.JSONDecodeError:
                    print(f"Non-JSON response: {response.text[:200]}...")
            else:
                print(f"Error response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")


def test_browser_automation():
    """Test browser automation approach"""
    print("\n" + "="*50)
    print("STEP 3: TESTING BROWSER AUTOMATION")
    print("="*50)
    
    options = Options()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("Navigating to h5.ele.me...")
        driver.get("https://h5.ele.me/")
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        print(f"Page title: {driver.title}")
        
        # Check for various elements
        selectors_to_check = [
            ("Restaurant elements", "//div[contains(@class, 'restaurant')]"),
            ("Shop elements", "//div[contains(@class, 'shop')]"),
            ("Food elements", "//div[contains(text(), '美食')]"),
            ("Location elements", "//div[contains(text(), '定位')]"),
            ("Login elements", "//div[contains(text(), '登录')]"),
            ("Any links", "//a[@href]"),
            ("Any buttons", "//button"),
            ("Any inputs", "//input")
        ]
        
        found_elements = {}
        
        for name, selector in selectors_to_check:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                found_elements[name] = len(elements)
                
                if elements:
                    print(f"✅ {name}: {len(elements)} found")
                    # Show sample text from first few elements
                    for i, element in enumerate(elements[:3]):
                        try:
                            text = element.text.strip()
                            if text:
                                print(f"   Sample {i+1}: {text[:50]}...")
                        except:
                            pass
                else:
                    print(f"❌ {name}: 0 found")
                    
            except Exception as e:
                print(f"⚠️  Error checking {name}: {e}")
        
        # Try to get page source snippet
        page_source = driver.page_source
        print(f"\nPage source length: {len(page_source)}")
        
        # Look for key indicators in page source
        indicators = ['餐厅', '外卖', '美食', '登录', 'restaurant', 'shop', 'login']
        for indicator in indicators:
            count = page_source.lower().count(indicator.lower())
            if count > 0:
                print(f"'{indicator}' appears {count} times in page source")
        
        return found_elements
        
    except Exception as e:
        print(f"❌ Browser automation failed: {e}")
        return {}
        
    finally:
        if driver:
            driver.quit()


def main():
    """Main test function"""
    print("🔍 Starting Eleme Integration Analysis...")
    
    # Test API endpoints
    test_api_endpoints()
    
    # Test browser automation
    browser_results = test_browser_automation()
    
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    
    print("📊 Analysis Results:")
    print("- API endpoints tested for direct data access")
    print("- Browser automation tested for fallback approach")
    print("- Page structure analyzed for element detection")
    
    if browser_results:
        total_elements = sum(browser_results.values())
        print(f"- Total elements found: {total_elements}")
        
        if total_elements > 0:
            print("✅ Some elements detected - partial success")
        else:
            print("❌ No relevant elements found")
    
    print("\n💡 Recommendations:")
    print("1. Focus on working API endpoints if any returned data")
    print("2. Implement login automation if all methods require authentication")
    print("3. Consider using Hyperbrowser tools for enhanced analysis")
    print("4. Explore alternative data sources or approaches")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
