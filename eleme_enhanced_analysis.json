{"login_analysis": {"phone_input": [], "sms_button": [], "login_button": [{"selector": "[class*='login']", "count": 1, "text": "v1.6.1", "placeholder": ""}]}, "url_results": {"https://www.ele.me/": {"title": "饿了么", "current_url": "https://www.ele.me/", "requires_login": false, "has_restaurants": true}, "https://h5.ele.me/shop/": {"title": "饿了么-登录", "current_url": "https://h5.ele.me/login/?redirect=https%3A%2F%2Fh5.ele.me%2Fminisite%2F%3Fspm%3Da2ogi.12117545.0.0&spm-pre=a2ogi.12117545.0.0&spm=a2f6g.12507204.ebridge.login", "requires_login": true, "has_restaurants": false}, "https://h5.ele.me/msite/": {"title": "饿了么-登录", "current_url": "https://h5.ele.me/login/?redirect=https%3A%2F%2Fh5.ele.me%2Fminisite%2F%3F&spm=a2f6g.12507204.ebridge.login", "requires_login": true, "has_restaurants": false}, "https://h5.ele.me/minisite/": {"title": "饿了么-登录", "current_url": "https://h5.ele.me/login/?redirect=https%3A%2F%2Fh5.ele.me%2Fminisite%2F&spm=a2f6g.12507204.ebridge.login", "requires_login": true, "has_restaurants": false}, "https://faas.ele.me/": {"title": "FaaS 前端自动化服务", "current_url": "https://faas.ele.me/", "requires_login": false, "has_restaurants": false}, "https://cube.elemecdn.com/": {"title": "C<PERSON>", "current_url": "https://cube.elemecdn.com/", "requires_login": false, "has_restaurants": false}}, "page_analysis": {"https://www.ele.me/": [{"selector": "//a[contains(@href, 'shop')]", "count": 1, "sample_text": "开放平台", "sample_href": "https://open.shop.ele.me/openapi"}, {"selector": "//ul", "count": 1, "sample_text": "增值电信业务经营许可证沪B2-20150033、B2-20221380\n食品经营许可证:JY13101070020255\n互联网药品信息服务资格证书:(沪)-经营性-2021-0083\n药品网络交易服", "sample_href": ""}, {"selector": "//li", "count": 11, "sample_text": "增值电信业务经营许可证沪B2-20150033、B2-20221380", "sample_href": ""}, {"selector": "//a[contains(@href, '/')]", "count": 22, "sample_text": "", "sample_href": "https://www.ele.me/"}], "https://faas.ele.me/": [{"selector": "//ul", "count": 1, "sample_text": "ElementQuizerFormnicSopushHttpizzaApp BuilderCrayfishConfig Hub", "sample_href": ""}, {"selector": "//li", "count": 8, "sample_text": "Element", "sample_href": ""}, {"selector": "//a[contains(@href, '/')]", "count": 13, "sample_text": "", "sample_href": "https://faas.ele.me/"}], "https://cube.elemecdn.com/": []}, "timestamp": 1752630730.1505363}