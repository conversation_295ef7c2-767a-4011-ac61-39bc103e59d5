#!/usr/bin/env python3
"""
Stable Eleme scraper with better error handling
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, InvalidSessionIdException


def setup_browser():
    """Setup browser with better stability"""
    options = Options()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-web-security')
    options.add_argument('--disable-features=VizDisplayCompositor')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
    
    # Keep browser open and stable
    options.add_experimental_option("detach", True)
    options.add_experimental_option("useAutomationExtension", False)
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def check_driver_status(driver):
    """Check if driver is still connected"""
    try:
        driver.current_url
        return True
    except (WebDriverException, InvalidSessionIdException):
        return False


def safe_get_element_info(element):
    """Safely extract element information"""
    try:
        info = {
            'text': '',
            'href': '',
            'class': '',
            'tag_name': '',
            'id': ''
        }
        
        try:
            info['text'] = element.text.strip()[:200] if element.text else ''
        except:
            pass
            
        try:
            info['href'] = element.get_attribute('href') or ''
        except:
            pass
            
        try:
            info['class'] = element.get_attribute('class') or ''
        except:
            pass
            
        try:
            info['tag_name'] = element.tag_name
        except:
            pass
            
        try:
            info['id'] = element.get_attribute('id') or ''
        except:
            pass
            
        return info
        
    except Exception as e:
        print(f"   提取元素信息时出错: {e}")
        return None


def extract_data_from_current_page(driver):
    """Extract data from whatever page is currently loaded"""
    if not check_driver_status(driver):
        print("❌ 浏览器连接已断开")
        return []
    
    print("🔍 开始从当前页面提取数据...")
    
    try:
        current_url = driver.current_url
        page_title = driver.title
        print(f"当前页面: {current_url}")
        print(f"页面标题: {page_title}")
    except:
        print("⚠️  无法获取页面信息")
        return []
    
    restaurants = []
    
    # 简化的选择器列表，专注于最可能的元素
    selectors = [
        # 餐厅容器
        "div[class*='restaurant']",
        "div[class*='shop']", 
        "div[class*='store']",
        "li[class*='restaurant']",
        "li[class*='shop']",
        "div[class*='merchant']",
        
        # 链接
        "a[href*='shop']",
        "a[href*='restaurant']",
        "a[href*='store']",
        
        # 通用容器
        "div[class*='item']",
        "li[class*='item']",
        "div[class*='card']"
    ]
    
    for selector in selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            if elements:
                print(f"✅ 找到 {len(elements)} 个元素: {selector}")
                
                for i, element in enumerate(elements[:10]):  # 限制数量避免过多
                    element_info = safe_get_element_info(element)
                    
                    if element_info and (element_info['text'] or element_info['href']):
                        element_info['selector'] = selector
                        element_info['index'] = i
                        restaurants.append(element_info)
                        
                        # 显示找到的内容预览
                        if element_info['text']:
                            print(f"   文本: {element_info['text'][:50]}...")
                        if element_info['href']:
                            print(f"   链接: {element_info['href']}")
            else:
                print(f"❌ 未找到: {selector}")
                
        except Exception as e:
            print(f"⚠️  选择器 {selector} 出错: {e}")
            continue
    
    # 尝试获取所有链接并过滤
    try:
        print("\n🔗 检查所有链接...")
        all_links = driver.find_elements(By.TAG_NAME, "a")
        restaurant_links = []
        
        keywords = ['shop', 'restaurant', 'store', 'food', 'merchant']
        text_keywords = ['餐厅', '外卖', '美食', '店铺', '商家', '菜品', '订餐']
        
        for link in all_links[:50]:  # 限制检查数量
            link_info = safe_get_element_info(link)
            
            if link_info:
                href = link_info['href'].lower()
                text = link_info['text']
                
                # 检查URL关键词
                if any(keyword in href for keyword in keywords):
                    link_info['type'] = 'url_match'
                    restaurant_links.append(link_info)
                    continue
                
                # 检查文本关键词
                if any(keyword in text for keyword in text_keywords):
                    link_info['type'] = 'text_match'
                    restaurant_links.append(link_info)
                    continue
        
        if restaurant_links:
            restaurants.extend(restaurant_links)
            print(f"✅ 找到 {len(restaurant_links)} 个餐厅相关链接")
            
    except Exception as e:
        print(f"⚠️  检查链接时出错: {e}")
    
    # 尝试查找带有数据属性的元素
    try:
        print("\n📊 检查数据属性...")
        data_selectors = [
            "[data-shop-id]",
            "[data-restaurant-id]", 
            "[data-store-id]",
            "[data-merchant-id]",
            "[data-id]"
        ]
        
        for selector in data_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"✅ 找到 {len(elements)} 个带数据属性的元素: {selector}")
                
                for element in elements[:5]:
                    element_info = safe_get_element_info(element)
                    if element_info:
                        element_info['selector'] = selector
                        element_info['type'] = 'data_attribute'
                        # 获取数据属性值
                        for attr in ['data-shop-id', 'data-restaurant-id', 'data-store-id', 'data-merchant-id', 'data-id']:
                            try:
                                value = element.get_attribute(attr)
                                if value:
                                    element_info[attr] = value
                            except:
                                pass
                        restaurants.append(element_info)
                        
    except Exception as e:
        print(f"⚠️  检查数据属性时出错: {e}")
    
    return restaurants


def main():
    """Main function with better error handling"""
    print("🚀 饿了么稳定版数据提取工具")
    print("="*50)
    
    driver = None
    
    try:
        # Setup browser
        print("🌐 启动浏览器...")
        driver = setup_browser()
        
        # Navigate to Eleme
        print("📱 导航到饿了么...")
        driver.get("https://h5.ele.me/")
        
        print("\n" + "="*60)
        print("🔐 请手动登录并导航到餐厅页面")
        print("="*60)
        print("1. 完成登录流程")
        print("2. 导航到有餐厅信息的页面（如首页、搜索结果等）")
        print("3. 确保页面完全加载")
        print("4. 然后按回车键开始数据提取...")
        print("="*60)
        
        input("准备好后按回车键...")
        
        # Check if browser is still connected
        if not check_driver_status(driver):
            print("❌ 浏览器连接已断开，请重新运行程序")
            return
        
        # Extract data
        restaurants = extract_data_from_current_page(driver)
        
        # Display results
        print(f"\n📊 提取结果")
        print("="*50)
        print(f"总共找到 {len(restaurants)} 个相关元素")
        
        if restaurants:
            # 按类型分组显示
            types = {}
            for restaurant in restaurants:
                rtype = restaurant.get('type', restaurant.get('selector', 'unknown'))
                if rtype not in types:
                    types[rtype] = []
                types[rtype].append(restaurant)
            
            print(f"\n📋 按类型分组:")
            for rtype, items in types.items():
                print(f"\n{rtype}: {len(items)} 个")
                for i, item in enumerate(items[:3], 1):  # 每类显示前3个
                    print(f"  {i}. 文本: {item.get('text', 'N/A')[:50]}...")
                    print(f"     链接: {item.get('href', 'N/A')}")
                    print(f"     类名: {item.get('class', 'N/A')[:30]}...")
            
            # Save results
            timestamp = int(time.time())
            filename = f"eleme_results_{timestamp}.json"
            
            try:
                results = {
                    'timestamp': timestamp,
                    'total_count': len(restaurants),
                    'types': {rtype: len(items) for rtype, items in types.items()},
                    'restaurants': restaurants
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                
                print(f"\n💾 结果已保存到: {filename}")
                print(f"✅ 数据提取完成!")
                
                # 分析建议
                if len(restaurants) > 0:
                    print(f"\n💡 分析建议:")
                    print(f"- 找到了 {len(restaurants)} 个元素，数据提取基本成功")
                    print(f"- 主要数据类型: {', '.join(types.keys())}")
                    print(f"- 建议重点关注带有链接的元素进行详细信息提取")
                    print(f"- 可以基于这些结果优化选择器和提取逻辑")
                
            except Exception as e:
                print(f"⚠️  保存结果时出错: {e}")
                
        else:
            print(f"\n⚠️  未找到餐厅相关数据")
            print(f"💡 建议:")
            print(f"- 确保已登录并在正确的页面")
            print(f"- 尝试导航到首页或搜索页面")
            print(f"- 检查页面是否完全加载")
        
        print(f"\n🔍 浏览器保持打开，可以手动检查页面结构")
        print(f"按 Ctrl+C 退出...")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n👋 退出程序...")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver and check_driver_status(driver):
            try:
                driver.quit()
            except:
                pass


if __name__ == "__main__":
    main()
