# Browser Configuration
HEADLESS_MODE=true
BROWSER_TIMEOUT=30
PAGE_LOAD_TIMEOUT=20

# Request Configuration
REQUEST_DELAY_MIN=1.0
REQUEST_DELAY_MAX=3.0
MAX_RETRIES=3

# Data Extraction Configuration
BATCH_SIZE=50
MAX_RESTAURANTS_PER_CITY=1000

# Database Configuration
DATABASE_URL=sqlite:///scraping_data.db
# For PostgreSQL: DATABASE_URL=postgresql://username:password@localhost:5432/scraping_db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=scraper.log
