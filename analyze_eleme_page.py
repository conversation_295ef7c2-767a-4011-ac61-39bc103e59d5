#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze Ele.me H5 page structure and update selectors
"""

import sys
import time
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import json


def setup_driver():
    """Setup Chrome driver for analysis"""
    options = Options()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--window-size=375,812')  # Mobile size
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # Mobile user agent
    options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver


def analyze_page_structure(driver):
    """Analyze the page structure and find relevant elements"""
    print("🔍 Analyzing Ele.me page structure...")
    
    # Navigate to Ele.me
    driver.get("https://h5.ele.me/")
    time.sleep(5)
    
    analysis_results = {
        "page_title": driver.title,
        "current_url": driver.current_url,
        "page_source_length": len(driver.page_source),
        "elements_found": {}
    }
    
    print(f"Page Title: {analysis_results['page_title']}")
    print(f"Current URL: {analysis_results['current_url']}")
    print(f"Page Source Length: {analysis_results['page_source_length']}")
    
    # Check for common elements
    element_checks = {
        "location_elements": [
            "//input[contains(@placeholder, '地址')]",
            "//input[contains(@placeholder, '位置')]", 
            "//div[contains(@class, 'address')]",
            "//div[contains(@class, 'location')]",
            "//span[contains(@class, 'address')]",
            "//button[contains(text(), '定位')]",
            "[data-testid*='location']",
            "[class*='location']",
            "[class*='address']"
        ],
        "restaurant_links": [
            "//a[contains(@href, '/shop/')]",
            "//a[contains(@href, '/restaurant/')]",
            "//div[contains(@class, 'restaurant')]//a",
            "//li[contains(@class, 'restaurant')]//a",
            "[href*='shop']",
            "[href*='restaurant']",
            "[class*='restaurant']",
            "[class*='shop']"
        ],
        "restaurant_list": [
            "//div[contains(@class, 'restaurant-list')]",
            "//ul[contains(@class, 'restaurant')]",
            "//div[contains(@class, 'shop-list')]",
            "[class*='restaurant-list']",
            "[class*='shop-list']",
            "[class*='food-list']"
        ]
    }
    
    for category, selectors in element_checks.items():
        print(f"\n--- Checking {category} ---")
        analysis_results["elements_found"][category] = []
        
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    element_info = {
                        "selector": selector,
                        "count": len(elements),
                        "sample_text": elements[0].text[:100] if elements[0].text else "",
                        "sample_html": elements[0].get_attribute('outerHTML')[:200] if elements else ""
                    }
                    analysis_results["elements_found"][category].append(element_info)
                    print(f"✅ Found {len(elements)} elements with: {selector}")
                    if elements[0].text:
                        print(f"   Sample text: {elements[0].text[:100]}")
                else:
                    print(f"❌ No elements found with: {selector}")
                    
            except Exception as e:
                print(f"⚠️  Error with selector {selector}: {e}")
    
    return analysis_results


def save_page_source(driver, filename="eleme_page_source.html"):
    """Save the page source for manual inspection"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"📄 Page source saved to {filename}")
    except Exception as e:
        print(f"❌ Error saving page source: {e}")


def main():
    """Main analysis function"""
    print("🚀 Starting Ele.me page structure analysis...")
    
    driver = setup_driver()
    
    try:
        # Analyze page structure
        results = analyze_page_structure(driver)
        
        # Save page source for manual inspection
        save_page_source(driver)
        
        # Save analysis results
        with open("eleme_analysis_results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print("📊 Analysis results saved to eleme_analysis_results.json")
        
        # Print summary
        print("\n" + "="*50)
        print("ANALYSIS SUMMARY")
        print("="*50)
        
        for category, elements in results["elements_found"].items():
            print(f"\n{category.upper()}:")
            if elements:
                for element in elements:
                    print(f"  ✅ {element['selector']} ({element['count']} found)")
            else:
                print("  ❌ No elements found")
                
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        
    finally:
        driver.quit()
        print("\n🏁 Analysis completed!")


if __name__ == "__main__":
    main()
