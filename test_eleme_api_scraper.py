#!/usr/bin/env python3
"""
Test script for the enhanced Eleme API scraper
"""

import sys
import json
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from scrapers.eleme_api_scraper import ElemeAPIScraper
from models import DatabaseManager
from config import config


def test_api_scraper():
    """Test the new API-based scraper"""
    print("🚀 Testing Enhanced Eleme API Scraper...")
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    # Initialize scraper
    scraper = ElemeAPIScraper(db_manager)
    
    try:
        print("\n" + "="*50)
        print("STEP 1: TESTING SESSION SETUP")
        print("="*50)
        
        success = scraper.setup_session()
        print(f"Session setup: {'✅ Success' if success else '❌ Failed'}")
        
        print("\n" + "="*50)
        print("STEP 2: TESTING LOCATION API")
        print("="*50)
        
        # Test Beijing coordinates
        location_data = scraper.get_location_data(39.9042, 116.4074)
        if location_data:
            print("✅ Location API returned data:")
            print(json.dumps(location_data, indent=2, ensure_ascii=False))
        else:
            print("❌ Location API failed")
        
        print("\n" + "="*50)
        print("STEP 3: TESTING BROWSER AUTOMATION FALLBACK")
        print("="*50)
        
        browser_data = scraper.try_browser_automation()
        if browser_data:
            print(f"✅ Browser automation found {len(browser_data)} elements:")
            for i, item in enumerate(browser_data[:5]):  # Show first 5
                print(f"  {i+1}. Text: {item.get('text', 'N/A')[:50]}...")
                print(f"     Href: {item.get('href', 'N/A')}")
                print(f"     Selector: {item.get('selector', 'N/A')}")
                print()
        else:
            print("❌ Browser automation found no elements")
        
        print("\n" + "="*50)
        print("STEP 4: TESTING FULL SCRAPING WORKFLOW")
        print("="*50)
        
        restaurants = scraper.scrape_restaurants("北京", limit=10)
        if restaurants:
            print(f"✅ Successfully scraped {len(restaurants)} restaurants:")
            for i, restaurant in enumerate(restaurants[:3]):  # Show first 3
                print(f"  {i+1}. Name: {restaurant.name}")
                print(f"     Category: {restaurant.category}")
                print(f"     Location: {restaurant.location}")
                print(f"     URL: {restaurant.url}")
                print()
        else:
            print("❌ No restaurants scraped")
        
        print("\n" + "="*50)
        print("STEP 5: TESTING URL EXTRACTION")
        print("="*50)
        
        urls = scraper.get_restaurant_urls("北京", limit=10)
        if urls:
            print(f"✅ Found {len(urls)} restaurant URLs:")
            for i, url in enumerate(urls[:5]):  # Show first 5
                print(f"  {i+1}. {url}")
        else:
            print("❌ No restaurant URLs found")
        
        print("\n" + "="*50)
        print("TEST SUMMARY")
        print("="*50)
        
        print(f"Session Setup: {'✅' if success else '❌'}")
        print(f"Location API: {'✅' if location_data else '❌'}")
        print(f"Browser Automation: {'✅' if browser_data else '❌'}")
        print(f"Restaurant Scraping: {'✅' if restaurants else '❌'}")
        print(f"URL Extraction: {'✅' if urls else '❌'}")
        
        if restaurants or browser_data or location_data:
            print("\n🎉 Overall: Some functionality is working!")
            print("💡 Recommendation: Focus on the working parts and improve from there.")
        else:
            print("\n⚠️  Overall: All methods failed")
            print("💡 Recommendation: May need to implement login automation or find alternative approaches.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🏁 Test completed!")


def test_with_hyperbrowser():
    """Test using Hyperbrowser for comparison"""
    print("\n" + "="*50)
    print("BONUS: TESTING WITH HYPERBROWSER")
    print("="*50)
    
    try:
        # This would use the Hyperbrowser tools we have available
        print("💡 Hyperbrowser integration can be added here for enhanced analysis")
        print("   - Use scrape_webpage_Hyperbrowser for page content")
        print("   - Use extract_structured_data_Hyperbrowser for data extraction")
        print("   - Use browser automation agents for complex interactions")
        
    except Exception as e:
        print(f"❌ Hyperbrowser test failed: {e}")


if __name__ == "__main__":
    test_api_scraper()
    test_with_hyperbrowser()
    
    print("\n" + "="*60)
    print("NEXT STEPS RECOMMENDATIONS")
    print("="*60)
    print("1. 🔐 Implement automated login if needed")
    print("2. 🔍 Analyze successful API responses to extract restaurant data")
    print("3. 🛠️  Integrate Hyperbrowser tools for enhanced scraping")
    print("4. 📊 Update database schema based on actual data structure")
    print("5. 🧪 Add comprehensive error handling and retry logic")
    print("6. ⚡ Optimize performance and add rate limiting")
    
    input("\nPress Enter to exit...")
