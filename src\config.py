"""
Configuration management for food delivery platform scraper
"""
import os
from typing import Dict, List
from pydantic import Field
from pydantic_settings import BaseSettings


class ScrapingConfig(BaseSettings):
    """Main configuration class for scraping system"""
    
    # Browser settings
    HEADLESS_MODE: bool = Field(True, env="HEADLESS_MODE")
    BROWSER_TIMEOUT: int = Field(30, env="BROWSER_TIMEOUT")
    PAGE_LOAD_TIMEOUT: int = Field(20, env="PAGE_LOAD_TIMEOUT")
    
    # Request settings
    REQUEST_DELAY_MIN: float = Field(1.0, env="REQUEST_DELAY_MIN")
    REQUEST_DELAY_MAX: float = Field(3.0, env="REQUEST_DELAY_MAX")
    MAX_RETRIES: int = Field(3, env="MAX_RETRIES")
    
    # Data extraction settings
    BATCH_SIZE: int = Field(50, env="BATCH_SIZE")
    MAX_RESTAURANTS_PER_CITY: int = Field(1000, env="MAX_RESTAURANTS_PER_CITY")
    
    # Database settings
    DATABASE_URL: str = Field("sqlite:///scraping_data.db", env="DATABASE_URL")
    
    # Logging settings
    LOG_LEVEL: str = Field("INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field("scraper.log", env="LOG_FILE")
    
    # Platform URLs
    ELEME_BASE_URL: str = "https://www.ele.me/"
    MEITUAN_BASE_URL: str = "https://h5.waimai.meituan.com/"
    DIANPING_BASE_URL: str = "https://www.dianping.com/"
    
    # Target cities for scraping
    TARGET_CITIES: List[str] = [
        "北京", "上海", "广州", "深圳", "杭州", 
        "南京", "武汉", "成都", "重庆", "西安"
    ]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class PlatformConfig:
    """Platform-specific configuration"""
    
    ELEME = {
        "name": "eleme",
        "display_name": "饿了么",
        "base_url": "https://www.ele.me/",
        "selectors": {
            "restaurant_list": ".restaurant-list-item",
            "restaurant_name": ".restaurant-name",
            "restaurant_category": ".restaurant-category",
            "restaurant_location": ".restaurant-location",
            "restaurant_price": ".restaurant-price",
            "restaurant_phone": ".restaurant-phone"
        },
        "required_fields": [
            "name", "category", "location", "price_range", "phone"
        ]
    }
    
    MEITUAN = {
        "name": "meituan",
        "display_name": "美团外卖",
        "base_url": "https://h5.waimai.meituan.com/",
        "selectors": {
            # To be filled during Meituan analysis phase
        },
        "required_fields": [
            "name", "category", "location", "price_range", "phone"
        ]
    }
    
    DIANPING = {
        "name": "dianping", 
        "display_name": "大众点评",
        "base_url": "https://www.dianping.com/",
        "selectors": {
            # To be filled during Dianping analysis phase
        },
        "required_fields": [
            "name", "category", "location", "price_range", "phone"
        ]
    }


# Global configuration instance
config = ScrapingConfig()

# Platform configurations
PLATFORMS = {
    "eleme": PlatformConfig.ELEME,
    "meituan": PlatformConfig.MEITUAN, 
    "dianping": PlatformConfig.DIANPING
}

# User agents for rotation
USER_AGENTS = [
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0",
    "Mozilla/5.0 (Android 10; Mobile; LG-M255; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.120 Mobile Safari/537.36"
]
