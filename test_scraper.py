#!/usr/bin/env python3
"""
Simple test script for the Ele.me scraper
Run this to validate the basic functionality before full deployment
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

import logging
from src.config import config
from src.models import DatabaseManager
from src.scrapers.eleme_scraper import ElemeScraper


def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )


def test_database_setup():
    """Test database initialization"""
    print("🔧 Testing database setup...")
    try:
        db_manager = DatabaseManager(config.DATABASE_URL)
        db_manager.create_tables()
        print("✅ Database setup successful")
        return db_manager
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return None


def test_scraper_initialization():
    """Test scraper initialization"""
    print("🔧 Testing scraper initialization...")
    try:
        db_manager = DatabaseManager(config.DATABASE_URL)
        scraper = ElemeScraper(db_manager)
        print("✅ Scraper initialization successful")
        return scraper
    except Exception as e:
        print(f"❌ Scraper initialization failed: {e}")
        return None


def test_browser_setup():
    """Test browser driver setup"""
    print("🔧 Testing browser setup...")
    try:
        db_manager = DatabaseManager(config.DATABASE_URL)
        scraper = ElemeScraper(db_manager)
        scraper.setup_driver()
        
        # Test basic navigation
        scraper.driver.get("https://www.baidu.com")
        title = scraper.driver.title
        scraper.close_driver()
        
        if title:
            print("✅ Browser setup successful")
            return True
        else:
            print("❌ Browser setup failed - no page title")
            return False
            
    except Exception as e:
        print(f"❌ Browser setup failed: {e}")
        return False


def test_eleme_navigation():
    """Test navigation to Ele.me"""
    print("🔧 Testing Ele.me navigation...")
    try:
        db_manager = DatabaseManager(config.DATABASE_URL)
        scraper = ElemeScraper(db_manager)
        scraper.setup_driver()
        
        # Test navigation to Ele.me
        success = scraper.navigate_to_city("北京")
        scraper.close_driver()
        
        if success:
            print("✅ Ele.me navigation successful")
            return True
        else:
            print("❌ Ele.me navigation failed")
            return False
            
    except Exception as e:
        print(f"❌ Ele.me navigation failed: {e}")
        return False


def test_basic_scraping():
    """Test basic scraping functionality"""
    print("🔧 Testing basic scraping (limited to 3 restaurants)...")
    try:
        db_manager = DatabaseManager(config.DATABASE_URL)
        db_manager.create_tables()
        
        scraper = ElemeScraper(db_manager)
        
        # Test scraping with very limited scope
        scraped_data = scraper.scrape_city("北京", limit=3)
        
        if scraped_data and len(scraped_data) > 0:
            print(f"✅ Basic scraping successful - {len(scraped_data)} restaurants scraped")
            
            # Show sample data
            for i, restaurant in enumerate(scraped_data[:2], 1):
                print(f"  Sample {i}: {restaurant.name} - {restaurant.category}")
                
            return True
        else:
            print("❌ Basic scraping failed - no data extracted")
            return False
            
    except Exception as e:
        print(f"❌ Basic scraping failed: {e}")
        return False


def run_comprehensive_test():
    """Run all tests in sequence"""
    print("🚀 Starting comprehensive scraper test...\n")
    
    setup_test_logging()
    
    tests = [
        ("Database Setup", test_database_setup),
        ("Scraper Initialization", test_scraper_initialization),
        ("Browser Setup", test_browser_setup),
        ("Ele.me Navigation", test_eleme_navigation),
        ("Basic Scraping", test_basic_scraping)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Scraper is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False


def quick_test():
    """Quick test for basic functionality"""
    print("🚀 Running quick functionality test...\n")
    
    setup_test_logging()
    
    # Test just the essentials
    db_ok = test_database_setup() is not None
    browser_ok = test_browser_setup()
    
    if db_ok and browser_ok:
        print("\n✅ Quick test passed! Basic functionality is working.")
        print("Run 'python test_scraper.py --full' for comprehensive testing.")
        return True
    else:
        print("\n❌ Quick test failed! Check your setup.")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--full":
        success = run_comprehensive_test()
    else:
        success = quick_test()
    
    sys.exit(0 if success else 1)
