#!/usr/bin/env python3
"""
Eleme scraper with manual login support - Short term solution
"""

import time
import json
import requests
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from typing import Dict, List, Optional


class ElemeManualLoginScraper:
    """
    Eleme scraper that allows manual login and then extracts data
    """
    
    def __init__(self):
        self.driver = None
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        
    def setup_driver(self):
        """Setup Chrome driver with proper options"""
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # Keep browser open for manual login
        options.add_experimental_option("detach", True)
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        self.logger.info("Chrome driver initialized")
        
    def manual_login_flow(self):
        """Guide user through manual login process"""
        try:
            self.logger.info("Starting manual login flow...")
            
            # Navigate to login page
            self.driver.get("https://h5.ele.me/")
            self.logger.info("Navigated to h5.ele.me")
            
            print("\n" + "="*60)
            print("🔐 MANUAL LOGIN REQUIRED")
            print("="*60)
            print("1. 浏览器已打开饿了么页面")
            print("2. 请手动完成登录流程：")
            print("   - 输入手机号")
            print("   - 获取并输入验证码")
            print("   - 完成滑块验证")
            print("   - 点击登录")
            print("3. 登录成功后，请在此处按回车键继续...")
            
            input("按回车键继续...")
            
            # Check if login was successful
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            self.logger.info(f"Current URL after login: {current_url}")
            self.logger.info(f"Page title: {page_title}")
            
            # Check for login success indicators
            if "login" not in current_url.lower() and "登录" not in page_title:
                self.logger.info("✅ Login appears successful!")
                return True
            else:
                self.logger.warning("⚠️  Login may not be complete")
                retry = input("是否重试？(y/n): ")
                if retry.lower() == 'y':
                    return self.manual_login_flow()
                return False
                
        except Exception as e:
            self.logger.error(f"Error in manual login flow: {e}")
            return False
    
    def extract_cookies_to_session(self):
        """Extract cookies from browser and add to requests session"""
        try:
            cookies = self.driver.get_cookies()
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
            
            self.logger.info(f"Extracted {len(cookies)} cookies to session")
            return True
            
        except Exception as e:
            self.logger.error(f"Error extracting cookies: {e}")
            return False
    
    def test_api_with_login(self):
        """Test API endpoints with login session"""
        results = {}
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://h5.ele.me/',
            'Origin': 'https://h5.ele.me'
        }
        
        # Test user session check with login
        try:
            url = "https://waimai-guide.ele.me/h5/mtop.alsc.user.session.ele.check/1.0/"
            params = {
                'jsv': '2.7.5',
                'appKey': '12574478',
                't': str(int(time.time() * 1000)),
                'api': 'mtop.alsc.user.session.ele.check',
                'v': '1.0',
                'type': 'originaljson',
                'dataType': 'json',
                'data': '{}'
            }
            
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results['user_session'] = data
                self.logger.info("✅ User session API call successful")
                
                # Check if we're now logged in
                error_code = data.get('data', {}).get('errorCode')
                if error_code != '000502':
                    self.logger.info("🎉 Login session is active!")
                else:
                    self.logger.warning("⚠️  Still showing login required")
            else:
                self.logger.error(f"User session API failed: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Error testing user session API: {e}")
        
        return results
    
    def scrape_current_page_data(self):
        """Scrape data from the current page after login"""
        try:
            self.logger.info("Analyzing current page for restaurant data...")
            
            # Wait for page to load
            time.sleep(3)
            
            # Get current page info
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            self.logger.info(f"Scraping page: {current_url}")
            self.logger.info(f"Page title: {page_title}")
            
            # Look for restaurant elements with various selectors
            restaurant_selectors = [
                # Common restaurant selectors
                "//div[contains(@class, 'restaurant')]",
                "//div[contains(@class, 'shop')]",
                "//div[contains(@class, 'store')]",
                "//a[contains(@href, 'shop')]",
                "//a[contains(@href, 'restaurant')]",
                
                # Text-based selectors
                "//div[contains(text(), '餐厅')]",
                "//div[contains(text(), '外卖')]",
                "//div[contains(text(), '美食')]",
                
                # CSS selectors
                "[class*='restaurant']",
                "[class*='shop']",
                "[class*='store']",
                "[class*='food']"
            ]
            
            found_restaurants = []
            
            for selector in restaurant_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        self.logger.info(f"✅ Found {len(elements)} elements with: {selector}")
                        
                        for i, element in enumerate(elements[:10]):  # Limit to first 10
                            try:
                                restaurant_data = {
                                    'selector': selector,
                                    'index': i,
                                    'text': element.text.strip() if element.text else '',
                                    'href': element.get_attribute('href') or '',
                                    'class': element.get_attribute('class') or '',
                                    'id': element.get_attribute('id') or '',
                                    'data_attributes': {}
                                }
                                
                                # Get data attributes
                                for attr in element.get_property('attributes') or []:
                                    attr_name = attr.get('name', '')
                                    if attr_name.startswith('data-'):
                                        restaurant_data['data_attributes'][attr_name] = attr.get('value', '')
                                
                                if restaurant_data['text'] or restaurant_data['href']:
                                    found_restaurants.append(restaurant_data)
                                    
                            except Exception as e:
                                self.logger.debug(f"Error processing element {i}: {e}")
                                continue
                    else:
                        self.logger.debug(f"❌ No elements found with: {selector}")
                        
                except Exception as e:
                    self.logger.debug(f"Error with selector {selector}: {e}")
                    continue
            
            # Also try to find any links that might lead to restaurants
            try:
                all_links = self.driver.find_elements(By.TAG_NAME, "a")
                restaurant_links = []
                
                for link in all_links:
                    href = link.get_attribute('href') or ''
                    text = link.text.strip()
                    
                    # Look for restaurant-related URLs or text
                    if any(keyword in href.lower() for keyword in ['shop', 'restaurant', 'store']) or \
                       any(keyword in text for keyword in ['餐厅', '外卖', '美食', '店铺']):
                        restaurant_links.append({
                            'type': 'restaurant_link',
                            'href': href,
                            'text': text,
                            'class': link.get_attribute('class') or ''
                        })
                
                if restaurant_links:
                    found_restaurants.extend(restaurant_links)
                    self.logger.info(f"✅ Found {len(restaurant_links)} restaurant-related links")
                    
            except Exception as e:
                self.logger.debug(f"Error finding restaurant links: {e}")
            
            self.logger.info(f"Total restaurant elements found: {len(found_restaurants)}")
            return found_restaurants
            
        except Exception as e:
            self.logger.error(f"Error scraping current page: {e}")
            return []
    
    def run_scraping_session(self):
        """Run the complete scraping session with manual login"""
        try:
            print("🚀 Starting Eleme Manual Login Scraper...")
            
            # Step 1: Setup browser
            self.setup_driver()
            
            # Step 2: Manual login
            if not self.manual_login_flow():
                print("❌ Login failed or cancelled")
                return
            
            # Step 3: Extract cookies
            if self.extract_cookies_to_session():
                print("✅ Cookies extracted successfully")
            
            # Step 4: Test APIs with login
            print("\n📡 Testing API endpoints with login session...")
            api_results = self.test_api_with_login()
            
            if api_results:
                print("✅ API testing completed")
                for endpoint, result in api_results.items():
                    print(f"   {endpoint}: {result.get('ret', ['Unknown'])}")
            
            # Step 5: Scrape current page
            print("\n🔍 Scraping current page for restaurant data...")
            restaurant_data = self.scrape_current_page_data()
            
            # Step 6: Display results
            print(f"\n📊 SCRAPING RESULTS")
            print("="*50)
            print(f"Found {len(restaurant_data)} restaurant-related elements")
            
            if restaurant_data:
                print("\n📋 Sample data (first 5 items):")
                for i, item in enumerate(restaurant_data[:5], 1):
                    print(f"\n{i}. Type: {item.get('type', 'element')}")
                    print(f"   Selector: {item.get('selector', 'N/A')}")
                    print(f"   Text: {item.get('text', 'N/A')[:100]}...")
                    print(f"   URL: {item.get('href', 'N/A')}")
                    print(f"   Class: {item.get('class', 'N/A')}")
                
                # Save results to file
                with open('eleme_scraping_results.json', 'w', encoding='utf-8') as f:
                    json.dump(restaurant_data, f, indent=2, ensure_ascii=False)
                print(f"\n💾 Results saved to 'eleme_scraping_results.json'")
                
                print(f"\n✅ Scraping completed successfully!")
                print(f"💡 Next steps: Analyze the data structure and improve extraction logic")
            else:
                print("\n⚠️  No restaurant data found")
                print("💡 Try navigating to a different page or check selectors")
            
            # Keep browser open for manual inspection
            print(f"\n🔍 Browser kept open for manual inspection")
            print(f"Press Ctrl+C to exit when done...")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n👋 Exiting...")
                
        except Exception as e:
            self.logger.error(f"Scraping session failed: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            if self.driver:
                self.driver.quit()


def main():
    scraper = ElemeManualLoginScraper()
    scraper.run_scraping_session()


if __name__ == "__main__":
    main()
