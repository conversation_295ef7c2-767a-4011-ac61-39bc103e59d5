#!/usr/bin/env python3
"""
Simple test script for Eleme data extraction after manual login
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By


def setup_browser():
    """Setup browser for manual login"""
    options = Options()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
    
    driver = webdriver.Chrome(options=options)
    return driver


def wait_for_manual_login(driver):
    """Wait for user to manually complete login"""
    print("\n" + "="*60)
    print("🔐 请手动登录饿了么")
    print("="*60)
    print("1. 浏览器已打开，请完成登录流程")
    print("2. 登录成功后，确保页面显示餐厅信息")
    print("3. 然后在这里按回车键继续数据提取...")
    print("="*60)
    
    input("登录完成后按回车键继续...")
    
    current_url = driver.current_url
    page_title = driver.title
    
    print(f"当前页面: {current_url}")
    print(f"页面标题: {page_title}")
    
    return True


def extract_restaurant_data(driver):
    """Extract restaurant data from current page"""
    print("\n🔍 开始提取餐厅数据...")
    
    # Wait for page to load
    time.sleep(2)
    
    restaurants = []
    
    # Method 1: Look for common restaurant elements
    selectors = [
        # Restaurant containers
        "div[class*='restaurant']",
        "div[class*='shop']", 
        "div[class*='store']",
        "li[class*='restaurant']",
        "li[class*='shop']",
        
        # Links to restaurants
        "a[href*='shop']",
        "a[href*='restaurant']",
        
        # Food/restaurant related text
        "div:contains('餐厅')",
        "div:contains('外卖')",
        "div:contains('美食')"
    ]
    
    for selector in selectors:
        try:
            if ':contains(' in selector:
                # XPath for text content
                xpath_selector = f"//*[contains(text(), '{selector.split(':contains(')[1].split(')')[0]}')]"
                elements = driver.find_elements(By.XPATH, xpath_selector)
            else:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            
            if elements:
                print(f"✅ 找到 {len(elements)} 个元素: {selector}")
                
                for i, element in enumerate(elements[:5]):  # 只取前5个
                    try:
                        restaurant_info = {
                            'selector': selector,
                            'index': i,
                            'text': element.text.strip()[:200],  # 限制文本长度
                            'href': element.get_attribute('href') or '',
                            'class': element.get_attribute('class') or '',
                            'tag_name': element.tag_name
                        }
                        
                        if restaurant_info['text'] or restaurant_info['href']:
                            restaurants.append(restaurant_info)
                            
                    except Exception as e:
                        print(f"   处理元素时出错: {e}")
                        continue
            else:
                print(f"❌ 未找到元素: {selector}")
                
        except Exception as e:
            print(f"⚠️  选择器错误 {selector}: {e}")
            continue
    
    # Method 2: Get all links and filter for restaurant-related ones
    try:
        all_links = driver.find_elements(By.TAG_NAME, "a")
        restaurant_links = []
        
        for link in all_links:
            href = link.get_attribute('href') or ''
            text = link.text.strip()
            
            # Filter for restaurant-related links
            if any(keyword in href.lower() for keyword in ['shop', 'restaurant', 'store', 'food']) or \
               any(keyword in text for keyword in ['餐厅', '外卖', '美食', '店铺', '商家']):
                restaurant_links.append({
                    'type': 'restaurant_link',
                    'href': href,
                    'text': text[:100],  # 限制文本长度
                    'class': link.get_attribute('class') or ''
                })
        
        if restaurant_links:
            restaurants.extend(restaurant_links)
            print(f"✅ 找到 {len(restaurant_links)} 个餐厅相关链接")
            
    except Exception as e:
        print(f"⚠️  提取链接时出错: {e}")
    
    # Method 3: Look for specific data attributes
    try:
        data_elements = driver.find_elements(By.CSS_SELECTOR, "[data-shop-id], [data-restaurant-id], [data-store-id]")
        if data_elements:
            print(f"✅ 找到 {len(data_elements)} 个带有店铺ID的元素")
            
            for element in data_elements[:5]:
                shop_data = {
                    'type': 'shop_with_id',
                    'shop_id': element.get_attribute('data-shop-id') or element.get_attribute('data-restaurant-id') or element.get_attribute('data-store-id'),
                    'text': element.text.strip()[:100],
                    'class': element.get_attribute('class') or ''
                }
                restaurants.append(shop_data)
                
    except Exception as e:
        print(f"⚠️  提取数据属性时出错: {e}")
    
    return restaurants


def analyze_page_structure(driver):
    """Analyze the page structure to understand data organization"""
    print("\n📊 分析页面结构...")
    
    try:
        # Get page info
        page_info = {
            'url': driver.current_url,
            'title': driver.title,
            'page_source_length': len(driver.page_source)
        }
        
        # Count common elements
        element_counts = {}
        common_tags = ['div', 'span', 'a', 'li', 'ul', 'section', 'article']
        
        for tag in common_tags:
            elements = driver.find_elements(By.TAG_NAME, tag)
            element_counts[tag] = len(elements)
        
        # Look for specific classes/ids that might contain restaurant data
        potential_containers = [
            "[class*='list']",
            "[class*='container']", 
            "[class*='content']",
            "[class*='main']",
            "[id*='list']",
            "[id*='container']"
        ]
        
        container_info = {}
        for selector in potential_containers:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    container_info[selector] = len(elements)
            except:
                continue
        
        print(f"页面URL: {page_info['url']}")
        print(f"页面标题: {page_info['title']}")
        print(f"页面源码长度: {page_info['page_source_length']}")
        print(f"元素统计: {element_counts}")
        print(f"潜在容器: {container_info}")
        
        return page_info
        
    except Exception as e:
        print(f"⚠️  页面结构分析出错: {e}")
        return {}


def save_results(restaurants, page_info):
    """Save extraction results to file"""
    results = {
        'timestamp': time.time(),
        'page_info': page_info,
        'restaurant_count': len(restaurants),
        'restaurants': restaurants
    }
    
    filename = f"eleme_extraction_results_{int(time.time())}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"💾 结果已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"⚠️  保存结果时出错: {e}")
        return None


def main():
    """Main function"""
    print("🚀 饿了么数据提取测试工具")
    print("="*50)
    
    driver = None
    
    try:
        # Setup browser
        print("🌐 启动浏览器...")
        driver = setup_browser()
        
        # Navigate to Eleme
        print("📱 导航到饿了么...")
        driver.get("https://h5.ele.me/")
        
        # Wait for manual login
        if not wait_for_manual_login(driver):
            print("❌ 登录流程取消")
            return
        
        # Analyze page structure
        page_info = analyze_page_structure(driver)
        
        # Extract restaurant data
        restaurants = extract_restaurant_data(driver)
        
        # Display results
        print(f"\n📊 提取结果")
        print("="*50)
        print(f"找到 {len(restaurants)} 个餐厅相关元素")
        
        if restaurants:
            print(f"\n📋 前5个结果预览:")
            for i, restaurant in enumerate(restaurants[:5], 1):
                print(f"\n{i}. 类型: {restaurant.get('type', '元素')}")
                print(f"   选择器: {restaurant.get('selector', 'N/A')}")
                print(f"   文本: {restaurant.get('text', 'N/A')[:50]}...")
                print(f"   链接: {restaurant.get('href', 'N/A')}")
                print(f"   类名: {restaurant.get('class', 'N/A')[:50]}...")
            
            # Save results
            filename = save_results(restaurants, page_info)
            
            print(f"\n✅ 数据提取完成!")
            print(f"💡 建议: 分析保存的JSON文件来优化提取逻辑")
            
        else:
            print(f"\n⚠️  未找到餐厅数据")
            print(f"💡 建议: 尝试导航到不同页面或检查页面是否完全加载")
        
        # Keep browser open for inspection
        print(f"\n🔍 浏览器保持打开状态，可以手动检查页面")
        print(f"按 Ctrl+C 退出...")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n👋 退出程序...")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            driver.quit()


if __name__ == "__main__":
    main()
