#!/usr/bin/env python3
"""
Detailed analysis of www.ele.me structure for restaurant data extraction
"""

import sys
import time
import json
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys


def setup_driver(headless=False):
    """Setup Chrome driver"""
    options = Options()
    
    if not headless:
        options.add_argument('--window-size=1200,800')
    else:
        options.add_argument('--headless')
        options.add_argument('--window-size=1200,800')
    
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # Desktop user agent for www.ele.me
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver


def analyze_main_page(driver):
    """Analyze the main www.ele.me page"""
    print("🔍 Analyzing www.ele.me main page...")
    
    driver.get("https://www.ele.me/")
    time.sleep(5)
    
    # Save page source for inspection
    with open("www_eleme_source.html", 'w', encoding='utf-8') as f:
        f.write(driver.page_source)
    
    analysis = {
        "title": driver.title,
        "url": driver.current_url,
        "page_type": "main_page"
    }
    
    # Look for location/city selection elements
    location_selectors = [
        "//input[contains(@placeholder, '请输入地址')]",
        "//input[contains(@placeholder, '地址')]",
        "//div[contains(@class, 'city')]",
        "//div[contains(@class, 'location')]",
        "//button[contains(text(), '定位')]",
        "//select[contains(@class, 'city')]",
        "[class*='city']",
        "[class*='location']",
        "[class*='address']"
    ]
    
    # Look for restaurant/food related elements
    restaurant_selectors = [
        "//a[contains(@href, 'restaurant')]",
        "//a[contains(@href, 'shop')]",
        "//div[contains(@class, 'restaurant')]",
        "//div[contains(@class, 'food')]",
        "//div[contains(@class, 'merchant')]",
        "[class*='restaurant']",
        "[class*='shop']",
        "[class*='food']",
        "[class*='merchant']"
    ]
    
    # Look for navigation/menu elements
    navigation_selectors = [
        "//nav",
        "//ul[contains(@class, 'menu')]",
        "//div[contains(@class, 'nav')]",
        "//a[contains(@class, 'nav')]",
        "[class*='nav']",
        "[class*='menu']"
    ]
    
    all_selectors = {
        "location": location_selectors,
        "restaurant": restaurant_selectors,
        "navigation": navigation_selectors
    }
    
    found_elements = {}
    
    for category, selectors in all_selectors.items():
        print(f"\n--- Checking {category} elements ---")
        found_elements[category] = []
        
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    element_info = {
                        "selector": selector,
                        "count": len(elements),
                        "sample_text": elements[0].text[:100] if elements[0].text else "",
                        "sample_href": elements[0].get_attribute('href') if elements[0].get_attribute('href') else "",
                        "sample_class": elements[0].get_attribute('class') if elements[0].get_attribute('class') else ""
                    }
                    found_elements[category].append(element_info)
                    print(f"✅ {selector}: {len(elements)} elements")
                    if elements[0].text:
                        print(f"   Text: {elements[0].text[:50]}...")
                    if elements[0].get_attribute('href'):
                        print(f"   Href: {elements[0].get_attribute('href')}")
                        
            except Exception as e:
                print(f"⚠️  Error with {selector}: {e}")
    
    analysis["elements"] = found_elements
    return analysis


def try_city_navigation(driver):
    """Try to navigate to a specific city page"""
    print("\n🏙️ Trying to navigate to city-specific content...")
    
    # Try different approaches to get to restaurant listings
    approaches = [
        {
            "name": "Direct city URL",
            "url": "https://www.ele.me/place/wtw3sm0q2te0",  # Beijing area code
            "description": "Try direct city URL"
        },
        {
            "name": "Search approach", 
            "url": "https://www.ele.me/",
            "description": "Try to use search functionality"
        }
    ]
    
    results = {}
    
    for approach in approaches:
        print(f"\n--- {approach['name']} ---")
        try:
            driver.get(approach['url'])
            time.sleep(5)
            
            result = {
                "final_url": driver.current_url,
                "title": driver.title,
                "has_restaurants": False,
                "restaurant_elements": []
            }
            
            # Look for restaurant elements
            restaurant_indicators = [
                "//div[contains(@class, 'restaurant')]",
                "//div[contains(@class, 'shop')]",
                "//a[contains(@href, 'restaurant')]",
                "//a[contains(@href, 'shop')]",
                "//div[contains(text(), '餐厅')]",
                "//div[contains(text(), '外卖')]",
                "[class*='restaurant']",
                "[class*='shop']"
            ]
            
            for selector in restaurant_indicators:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        result["has_restaurants"] = True
                        result["restaurant_elements"].append({
                            "selector": selector,
                            "count": len(elements),
                            "sample_text": elements[0].text[:100] if elements[0].text else ""
                        })
                        print(f"✅ Found restaurants with: {selector} ({len(elements)} elements)")
                        
                except Exception as e:
                    continue
            
            results[approach['name']] = result
            
            # Save page source if restaurants found
            if result["has_restaurants"]:
                filename = f"eleme_{approach['name'].replace(' ', '_').lower()}_source.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(driver.page_source)
                print(f"📄 Page source saved to {filename}")
            
        except Exception as e:
            print(f"❌ Error with {approach['name']}: {e}")
            results[approach['name']] = {"error": str(e)}
    
    return results


def main():
    """Main analysis function"""
    print("🚀 Starting detailed www.ele.me analysis...")
    
    driver = setup_driver(headless=False)
    
    try:
        # Step 1: Analyze main page
        print("\n" + "="*50)
        print("STEP 1: ANALYZING MAIN PAGE")
        print("="*50)
        
        main_analysis = analyze_main_page(driver)
        
        # Step 2: Try city navigation
        print("\n" + "="*50)
        print("STEP 2: TRYING CITY NAVIGATION")
        print("="*50)
        
        city_results = try_city_navigation(driver)
        
        # Combine results
        final_results = {
            "main_page": main_analysis,
            "city_navigation": city_results,
            "timestamp": time.time()
        }
        
        # Save results
        with open("www_eleme_detailed_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        print("\n" + "="*50)
        print("ANALYSIS COMPLETE")
        print("="*50)
        print("📊 Results saved to www_eleme_detailed_analysis.json")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"- Main page title: {main_analysis['title']}")
        print(f"- Location elements found: {len(main_analysis['elements']['location'])}")
        print(f"- Restaurant elements found: {len(main_analysis['elements']['restaurant'])}")
        print(f"- Navigation elements found: {len(main_analysis['elements']['navigation'])}")
        
        for approach, result in city_results.items():
            if not result.get('error'):
                print(f"- {approach} has restaurants: {result.get('has_restaurants', False)}")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()
        print("🏁 Analysis completed!")


if __name__ == "__main__":
    main()
