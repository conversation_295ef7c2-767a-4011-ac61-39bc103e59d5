#!/usr/bin/env python3
"""
Enhanced Ele.me scraper with login handling
"""

import sys
import time
import json
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys


def setup_driver(headless=False):
    """Setup Chrome driver with enhanced options"""
    options = Options()
    
    if not headless:
        options.add_argument('--window-size=1200,800')
    else:
        options.add_argument('--headless')
        options.add_argument('--window-size=375,812')
    
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # Mobile user agent for better compatibility
    options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver


def handle_login_page(driver):
    """Handle the login page - for now just analyze what's available"""
    print("🔐 Analyzing login page...")
    
    # Wait for page to load
    time.sleep(3)
    
    # Look for login elements
    login_elements = {
        "phone_input": [
            "//input[@type='tel']",
            "//input[contains(@placeholder, '手机')]",
            "//input[contains(@placeholder, '电话')]",
            "[placeholder*='手机']",
            "[type='tel']"
        ],
        "sms_button": [
            "//button[contains(text(), '验证码')]",
            "//button[contains(text(), '获取')]",
            "[class*='sms']",
            "[class*='code']"
        ],
        "login_button": [
            "//button[contains(text(), '登录')]",
            "//button[contains(text(), '确认')]",
            "[class*='login']",
            "[class*='submit']"
        ]
    }
    
    found_elements = {}
    
    for category, selectors in login_elements.items():
        print(f"\n--- Checking {category} ---")
        found_elements[category] = []
        
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    element_info = {
                        "selector": selector,
                        "count": len(elements),
                        "text": elements[0].text if elements[0].text else "",
                        "placeholder": elements[0].get_attribute('placeholder') if elements[0].get_attribute('placeholder') else ""
                    }
                    found_elements[category].append(element_info)
                    print(f"✅ Found {len(elements)} elements: {selector}")
                    if elements[0].text:
                        print(f"   Text: {elements[0].text}")
                    if elements[0].get_attribute('placeholder'):
                        print(f"   Placeholder: {elements[0].get_attribute('placeholder')}")
                        
            except Exception as e:
                print(f"⚠️  Error with selector {selector}: {e}")
    
    return found_elements


def try_alternative_urls(driver):
    """Try alternative URLs that might not require login"""
    alternative_urls = [
        "https://www.ele.me/",
        "https://h5.ele.me/shop/",
        "https://h5.ele.me/msite/",
        "https://h5.ele.me/minisite/",
        "https://faas.ele.me/",
        "https://cube.elemecdn.com/"
    ]
    
    results = {}
    
    for url in alternative_urls:
        print(f"\n🔍 Trying URL: {url}")
        try:
            driver.get(url)
            time.sleep(3)
            
            results[url] = {
                "title": driver.title,
                "current_url": driver.current_url,
                "requires_login": "登录" in driver.title or "login" in driver.current_url.lower(),
                "has_restaurants": len(driver.find_elements(By.XPATH, "//*[contains(text(), '餐厅') or contains(text(), '外卖')]")) > 0
            }
            
            print(f"   Title: {driver.title}")
            print(f"   Final URL: {driver.current_url}")
            print(f"   Requires Login: {results[url]['requires_login']}")
            print(f"   Has Restaurant Content: {results[url]['has_restaurants']}")
            
        except Exception as e:
            print(f"   ❌ Error accessing {url}: {e}")
            results[url] = {"error": str(e)}
    
    return results


def analyze_page_after_navigation(driver):
    """Analyze page structure after successful navigation"""
    print("\n🔍 Analyzing page structure...")
    
    # Common selectors to check
    selectors_to_check = [
        # Restaurant related
        "//div[contains(@class, 'restaurant')]",
        "//div[contains(@class, 'shop')]", 
        "//a[contains(@href, 'shop')]",
        "//a[contains(@href, 'restaurant')]",
        
        # Location related
        "//div[contains(@class, 'location')]",
        "//div[contains(@class, 'address')]",
        "//input[contains(@placeholder, '地址')]",
        
        # General content
        "//div[contains(@class, 'list')]",
        "//ul",
        "//li",
        "//a[contains(@href, '/')]"
    ]
    
    found_elements = []
    
    for selector in selectors_to_check:
        try:
            elements = driver.find_elements(By.XPATH, selector)
            if elements:
                found_elements.append({
                    "selector": selector,
                    "count": len(elements),
                    "sample_text": elements[0].text[:100] if elements[0].text else "",
                    "sample_href": elements[0].get_attribute('href') if elements[0].get_attribute('href') else ""
                })
                print(f"✅ {selector}: {len(elements)} elements")
                
        except Exception as e:
            print(f"⚠️  Error with {selector}: {e}")
    
    return found_elements


def main():
    """Main analysis function"""
    print("🚀 Starting enhanced Ele.me analysis...")
    
    # Try with visible browser first for debugging
    driver = setup_driver(headless=False)
    
    try:
        # Step 1: Analyze the main login page
        print("\n" + "="*50)
        print("STEP 1: ANALYZING LOGIN PAGE")
        print("="*50)
        
        driver.get("https://h5.ele.me/")
        login_analysis = handle_login_page(driver)
        
        # Step 2: Try alternative URLs
        print("\n" + "="*50)
        print("STEP 2: TRYING ALTERNATIVE URLS")
        print("="*50)
        
        url_results = try_alternative_urls(driver)
        
        # Step 3: Find a working URL and analyze it
        print("\n" + "="*50)
        print("STEP 3: ANALYZING ACCESSIBLE PAGES")
        print("="*50)
        
        accessible_urls = [url for url, data in url_results.items() 
                          if not data.get('requires_login', True) and not data.get('error')]
        
        page_analysis = {}
        for url in accessible_urls:
            print(f"\n--- Analyzing {url} ---")
            driver.get(url)
            time.sleep(3)
            page_analysis[url] = analyze_page_after_navigation(driver)
        
        # Save results
        results = {
            "login_analysis": login_analysis,
            "url_results": url_results,
            "page_analysis": page_analysis,
            "timestamp": time.time()
        }
        
        with open("eleme_enhanced_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print("\n" + "="*50)
        print("ANALYSIS COMPLETE")
        print("="*50)
        print("📊 Results saved to eleme_enhanced_analysis.json")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"- Login required for main site: Yes")
        print(f"- Alternative URLs tested: {len(alternative_urls)}")
        print(f"- Accessible URLs found: {len(accessible_urls)}")
        
        if accessible_urls:
            print(f"- Accessible URLs: {', '.join(accessible_urls)}")
        else:
            print("- No accessible URLs found without login")
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()
        print("🏁 Analysis completed!")


if __name__ == "__main__":
    main()
