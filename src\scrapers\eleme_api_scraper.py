"""
Enhanced Eleme scraper using discovered API endpoints
"""
import time
import json
import requests
import logging
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from base_scraper import BaseScraper
from models import RestaurantData


class ElemeAPIScraper(BaseScraper):
    """Enhanced Eleme scraper using API endpoints"""
    
    def __init__(self, database_manager):
        super().__init__("eleme_api", database_manager)
        self.session = requests.Session()
        self.api_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://h5.ele.me/',
            'Origin': 'https://h5.ele.me'
        }
        
    def setup_session(self):
        """Setup session with proper headers and cookies"""
        try:
            # First, visit the main page to get initial cookies
            response = self.session.get('https://h5.ele.me/', headers=self.api_headers)
            self.logger.info(f"Initial page response: {response.status_code}")
            
            # Try to get session info
            session_url = "https://waimai-guide.ele.me/h5/mtop.alsc.user.session.ele.check/1.0/"
            session_response = self.session.get(session_url, headers=self.api_headers)
            self.logger.info(f"Session check response: {session_response.status_code}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup session: {e}")
            return False
    
    def get_location_data(self, latitude: float = 39.9042, longitude: float = 116.4074):
        """Get location-based restaurant data"""
        try:
            # This is a discovered API endpoint for location-based queries
            api_url = "https://alsc-buy2.ele.me/h5/mtop.alsc.waimai.store.detail.mstie.scheme.query/1.0/"
            
            params = {
                'jsv': '2.6.2',
                'appKey': '12574478',
                't': str(int(time.time() * 1000)),
                'api': 'mtop.alsc.waimai.store.detail.mstie.scheme.query',
                'v': '1.0',
                'dataType': 'json',
                'type': 'originaljson',
                'data': json.dumps({
                    'latitude': latitude,
                    'longitude': longitude,
                    'type': 1,
                    'defaultSwitch': True
                })
            }
            
            response = self.session.get(api_url, params=params, headers=self.api_headers)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.logger.info(f"Location API response: {data}")
                    return data
                except json.JSONDecodeError:
                    self.logger.warning("Failed to parse JSON response")
                    return None
            else:
                self.logger.warning(f"Location API returned status: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting location data: {e}")
            return None
    
    def try_browser_automation(self):
        """Fallback to browser automation if API fails"""
        try:
            self.setup_driver()
            
            # Navigate to the main page
            self.driver.get("https://h5.ele.me/")
            time.sleep(5)
            
            # Check if we can find any restaurant elements
            restaurant_selectors = [
                "//div[contains(@class, 'restaurant')]",
                "//div[contains(@class, 'shop')]",
                "//a[contains(@href, 'shop')]",
                "//div[contains(text(), '餐厅')]",
                "[class*='restaurant']",
                "[class*='shop']"
            ]
            
            found_restaurants = []
            
            for selector in restaurant_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        self.logger.info(f"Found {len(elements)} elements with selector: {selector}")
                        for element in elements[:5]:  # Limit to first 5
                            try:
                                text = element.text
                                href = element.get_attribute('href')
                                if text or href:
                                    found_restaurants.append({
                                        'text': text,
                                        'href': href,
                                        'selector': selector
                                    })
                            except:
                                continue
                                
                except Exception as e:
                    self.logger.debug(f"Error with selector {selector}: {e}")
                    continue
            
            return found_restaurants
            
        except Exception as e:
            self.logger.error(f"Browser automation failed: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()
    
    def scrape_restaurants(self, location: str = "北京", limit: int = 50) -> List[RestaurantData]:
        """Main scraping method"""
        restaurants = []
        
        try:
            # Step 1: Setup session
            if not self.setup_session():
                self.logger.warning("Session setup failed, continuing anyway...")
            
            # Step 2: Try API approach
            self.logger.info("Trying API approach...")
            api_data = self.get_location_data()
            
            if api_data:
                self.logger.info("API approach successful, processing data...")
                # Process API data here
                # This would need to be implemented based on actual API response structure
                pass
            else:
                self.logger.info("API approach failed, trying browser automation...")
                
                # Step 3: Fallback to browser automation
                browser_data = self.try_browser_automation()
                
                if browser_data:
                    self.logger.info(f"Found {len(browser_data)} potential restaurant elements")
                    
                    # Convert browser data to RestaurantData objects
                    for item in browser_data:
                        try:
                            restaurant = RestaurantData(
                                name=item.get('text', 'Unknown'),
                                category='未知',
                                location=location,
                                price_range='未知',
                                phone='未知',
                                platform='eleme',
                                url=item.get('href', ''),
                                raw_data=item
                            )
                            restaurants.append(restaurant)
                        except Exception as e:
                            self.logger.debug(f"Error creating restaurant data: {e}")
                            continue
                else:
                    self.logger.warning("Both API and browser automation failed")
            
            self.logger.info(f"Successfully scraped {len(restaurants)} restaurants")
            return restaurants
            
        except Exception as e:
            self.logger.error(f"Scraping failed: {e}")
            return restaurants
    
    def get_restaurant_urls(self, location: str = "北京", limit: int = 50) -> List[str]:
        """Get restaurant URLs for detailed scraping"""
        urls = []
        
        try:
            restaurants = self.scrape_restaurants(location, limit)
            for restaurant in restaurants:
                if restaurant.url and restaurant.url.startswith('http'):
                    urls.append(restaurant.url)
            
            self.logger.info(f"Found {len(urls)} restaurant URLs")
            return urls
            
        except Exception as e:
            self.logger.error(f"Error getting restaurant URLs: {e}")
            return urls
    
    def scrape_restaurant_details(self, url: str) -> Optional[RestaurantData]:
        """Scrape detailed information from a restaurant page"""
        try:
            # This would need to be implemented based on actual restaurant page structure
            self.logger.info(f"Scraping details for: {url}")
            
            # For now, return None as we need to analyze actual restaurant pages
            return None
            
        except Exception as e:
            self.logger.error(f"Error scraping restaurant details from {url}: {e}")
            return None
