#!/usr/bin/env python3
"""
Explore actual waimai (food delivery) pages on ele.me
"""

import sys
import time
import json
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys


def setup_driver(headless=False):
    """Setup Chrome driver"""
    options = Options()
    
    if not headless:
        options.add_argument('--window-size=1200,800')
    else:
        options.add_argument('--headless')
        options.add_argument('--window-size=1200,800')
    
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # Desktop user agent
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    
    driver = webdriver.Chrome(options=options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver


def test_waimai_urls(driver):
    """Test various waimai-related URLs"""
    
    urls_to_test = [
        "https://www.ele.me/waimai",
        "https://www.ele.me/waimai/",
        "https://waimai.ele.me/",
        "https://h5.ele.me/waimai",
        "https://h5.ele.me/waimai/",
        "https://m.ele.me/",
        "https://m.ele.me/waimai",
        "https://www.ele.me/place/wtw3sm0q2te0",  # Beijing area
        "https://www.ele.me/shop",
        "https://www.ele.me/restaurant"
    ]
    
    results = {}
    
    for url in urls_to_test:
        print(f"\n🔍 Testing URL: {url}")
        try:
            driver.get(url)
            time.sleep(5)
            
            result = {
                "original_url": url,
                "final_url": driver.current_url,
                "title": driver.title,
                "requires_login": "登录" in driver.title or "login" in driver.current_url.lower(),
                "page_analysis": {}
            }
            
            print(f"   Final URL: {result['final_url']}")
            print(f"   Title: {result['title']}")
            print(f"   Requires Login: {result['requires_login']}")
            
            # Analyze page content
            if not result['requires_login']:
                result['page_analysis'] = analyze_page_content(driver)
            
            results[url] = result
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[url] = {"error": str(e)}
    
    return results


def analyze_page_content(driver):
    """Analyze page content for restaurant-related elements"""
    
    analysis = {
        "restaurant_elements": [],
        "location_elements": [],
        "menu_elements": [],
        "interactive_elements": []
    }
    
    # Restaurant-related selectors
    restaurant_selectors = [
        "//div[contains(@class, 'restaurant')]",
        "//div[contains(@class, 'shop')]",
        "//a[contains(@href, 'restaurant')]",
        "//a[contains(@href, 'shop')]",
        "//div[contains(text(), '餐厅')]",
        "//div[contains(text(), '外卖')]",
        "//div[contains(text(), '美食')]",
        "[class*='restaurant']",
        "[class*='shop']",
        "[class*='food']"
    ]
    
    # Location-related selectors
    location_selectors = [
        "//input[contains(@placeholder, '地址')]",
        "//input[contains(@placeholder, '位置')]",
        "//div[contains(@class, 'location')]",
        "//div[contains(@class, 'address')]",
        "//button[contains(text(), '定位')]",
        "[class*='location']",
        "[class*='address']"
    ]
    
    # Menu/navigation selectors
    menu_selectors = [
        "//nav",
        "//ul[contains(@class, 'menu')]",
        "//div[contains(@class, 'nav')]",
        "[class*='nav']",
        "[class*='menu']"
    ]
    
    # Interactive elements
    interactive_selectors = [
        "//button",
        "//input",
        "//select",
        "//a[contains(@href, '/')]"
    ]
    
    selector_groups = {
        "restaurant_elements": restaurant_selectors,
        "location_elements": location_selectors,
        "menu_elements": menu_selectors,
        "interactive_elements": interactive_selectors
    }
    
    for group_name, selectors in selector_groups.items():
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    element_info = {
                        "selector": selector,
                        "count": len(elements),
                        "sample_text": elements[0].text[:100] if elements[0].text else "",
                        "sample_href": elements[0].get_attribute('href') if elements[0].get_attribute('href') else "",
                        "sample_class": elements[0].get_attribute('class') if elements[0].get_attribute('class') else ""
                    }
                    analysis[group_name].append(element_info)
                    
            except Exception as e:
                continue
    
    return analysis


def try_mobile_simulation(driver):
    """Try accessing with mobile user agent"""
    print("\n📱 Testing with mobile user agent...")
    
    # Set mobile user agent
    driver.execute_cdp_cmd('Network.setUserAgentOverride', {
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
    })
    
    mobile_urls = [
        "https://h5.ele.me/",
        "https://m.ele.me/",
        "https://www.ele.me/waimai"
    ]
    
    mobile_results = {}
    
    for url in mobile_urls:
        print(f"\n📱 Mobile test: {url}")
        try:
            driver.get(url)
            time.sleep(5)
            
            result = {
                "url": url,
                "final_url": driver.current_url,
                "title": driver.title,
                "requires_login": "登录" in driver.title or "login" in driver.current_url.lower()
            }
            
            print(f"   Final URL: {result['final_url']}")
            print(f"   Title: {result['title']}")
            print(f"   Requires Login: {result['requires_login']}")
            
            mobile_results[url] = result
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            mobile_results[url] = {"error": str(e)}
    
    return mobile_results


def main():
    """Main exploration function"""
    print("🚀 Starting waimai page exploration...")
    
    driver = setup_driver(headless=False)
    
    try:
        # Step 1: Test desktop URLs
        print("\n" + "="*50)
        print("STEP 1: TESTING DESKTOP URLS")
        print("="*50)
        
        desktop_results = test_waimai_urls(driver)
        
        # Step 2: Test mobile simulation
        print("\n" + "="*50)
        print("STEP 2: TESTING MOBILE SIMULATION")
        print("="*50)
        
        mobile_results = try_mobile_simulation(driver)
        
        # Combine results
        final_results = {
            "desktop_results": desktop_results,
            "mobile_results": mobile_results,
            "timestamp": time.time()
        }
        
        # Save results
        with open("waimai_exploration_results.json", 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        print("\n" + "="*50)
        print("EXPLORATION COMPLETE")
        print("="*50)
        print("📊 Results saved to waimai_exploration_results.json")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        accessible_urls = []
        for url, data in desktop_results.items():
            if not data.get('error') and not data.get('requires_login', True):
                accessible_urls.append(url)
        
        print(f"- Desktop URLs tested: {len(desktop_results)}")
        print(f"- Mobile URLs tested: {len(mobile_results)}")
        print(f"- Accessible URLs (no login): {len(accessible_urls)}")
        
        if accessible_urls:
            print("- Accessible URLs:")
            for url in accessible_urls:
                print(f"  • {url}")
        
    except Exception as e:
        print(f"❌ Exploration failed: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()
        print("🏁 Exploration completed!")


if __name__ == "__main__":
    main()
